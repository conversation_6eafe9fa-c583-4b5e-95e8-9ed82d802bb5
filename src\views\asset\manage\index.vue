<template>
    <div class="app-container">

        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="物料编码" prop="materialCode">
                <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="使用人" prop="nickName">
                <el-input v-model="queryParams.nickName" placeholder="请输入使用人" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="资产名称" prop="assetName">
                <el-input v-model="queryParams.assetName" placeholder="请输入资产名称" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
                <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
                    start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['system:user:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['system:user:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['system:user:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Upload" @click="handleImport"
                    v-hasPermi="['system:user:import']">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['system:user:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>



        <el-table v-loading="loading" :data="assetList">
            <el-table-column prop="id" label="序号" width="150"></el-table-column>
            <el-table-column prop="materialName" label="资产类别" width="260"></el-table-column>
            <el-table-column prop="assetName" label="资产名称" width="200"></el-table-column>
            <el-table-column prop="specs" label="规格型号" width="200"></el-table-column>
            <el-table-column prop="netAssetValue" label="资产净值" width="100"></el-table-column>
            <el-table-column prop="purchasePrice" label="采购价格" width="100"></el-table-column>
            <el-table-column prop="nickName" label="使用人" width="200"></el-table-column>
            <el-table-column prop="location" label="资产位置" width="200"></el-table-column>
            <el-table-column prop="remark" label="备注"></el-table-column>
            <el-table-column prop="status" label="状态" width="100"></el-table-column>
            <el-table-column prop="updateTime" label="更新时间" width="200"></el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleDetails(scope.row)">详情</el-button>
                    <el-button v-if="scope.row.parentId != 0" link type="primary" icon="Delete"
                        @click="handleDelete(scope.row)" v-hasPermi="['system:dept:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加资产对话框 -->
        <el-dialog :title="title" v-model="openAdd" width="60%" append-to-body>
            <el-form :model="form" :rules="rules" ref="addRef" label-width="80px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="资产名称" prop="assetName">
                            <el-input v-model="form.assetName" placeholder="请输入资产名称" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="资产类别" prop="materialCode">
                            <el-tree-select v-model="form.materialCode" :data="materialTypeOptions"
                                :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id"
                                placeholder="请选择资产类型" check-strictly />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="规格信息" prop="specs">
                            <el-input v-model="form.specs" placeholder="请输入规格信息" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计量单位" prop="unit">
                            <el-input v-model="form.unit" placeholder="请输入计量单位" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="采购价格" prop="purchasePrice">
                            <el-input v-model="form.purchasePrice" placeholder="请输入采购价格" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="资产净值" prop="netAssetValue">
                            <el-input v-model="form.netAssetValue" placeholder="请输入资产净值" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="使用人" prop="nickName">
                            <el-input v-model="form.nickName" placeholder="请输入使用人" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="资产位置" prop="location">
                            <el-input v-model="form.location" placeholder="请输入资产位置" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="使用年限" prop="usefulLifeYears">
                            <el-input v-model="form.usefulLifeYears" placeholder="请输入使用年限" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="残值年限" prop="residualLifeYears">
                            <el-input v-model="form.residualLifeYears" placeholder="请输入残值年限" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="状态">
                            <el-radio-group v-model="form.status">
                                <el-radio v-for="dict in asset_normal_status" :key="dict.value" :value="dict.value">{{
                                    dict.label
                                    }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 资产详情对话框 -->
        <el-dialog :title="title" v-model="openDetails" width="60%" append-to-body>
            <el-form :model="form" :rules="rules" ref="detailRef" label-width="80px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="资产名称" prop="assetName">
                            <el-input v-model="form.assetName" placeholder="请输入资产名称" maxlength="30" />
                        </el-form-item>
                        <el-form-item label="资产类别" prop="materialCode">
                            <el-tree-select v-model="form.materialCode" :data="materialTypeOptions"
                                :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id"
                                placeholder="请选择资产类型" check-strictly />
                        </el-form-item>
                        <el-form-item label="规格信息" prop="specs">
                            <el-input v-model="form.specs" placeholder="请输入规格信息" maxlength="30" />
                        </el-form-item>
                        <el-form-item label="计量单位" prop="unit">
                            <el-input v-model="form.unit" placeholder="请输入计量单位" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="图片" prop="imgUrl">
                            <el-upload class="img-uploader" :show-file-list="false" :auto-upload="false"
                                :on-change="handleChange">
                                <img v-if="form.imgUrl" :src="form.imgUrl" class="img" />
                                <img v-else-if="tempImageUrl" :src="tempImageUrl" class="img" />
                                <el-icon v-else class="img-uploader-icon">
                                    <Plus />
                                </el-icon>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="二维码" prop="qrcode">
                            <div id="qrcode" ref="qrcode"></div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="采购价格" prop="purchasePrice">
                            <el-input v-model="form.purchasePrice" placeholder="请输入采购价格" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="资产净值" prop="netAssetValue">
                            <el-input v-model="form.netAssetValue" placeholder="请输入资产净值" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="使用人" prop="nickName">
                            <el-input v-model="form.nickName" placeholder="请输入使用人" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="资产位置" prop="location">
                            <el-input v-model="form.location" placeholder="请输入资产位置" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="使用年限" prop="usefulLifeYears">
                            <el-input v-model="form.usefulLifeYears" placeholder="请输入使用年限" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="残值年限" prop="residualLifeYears">
                            <el-input v-model="form.residualLifeYears" placeholder="请输入残值年限" maxlength="30" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="剩余年限">
                            <el-input v-model="remainingYears" placeholder="请输入残值年限" maxlength="30" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态">
                            <el-radio-group v-model="form.status">
                                <el-radio v-for="dict in asset_normal_status" :key="dict.value" :value="dict.value">{{
                                    dict.label
                                    }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建人" prop="createBy">
                            <el-input v-model="form.createBy" placeholder="请输入创建人" maxlength="30" disabled/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="创建时间" prop="createTime">
                            <el-input v-model="form.createTime" placeholder="请输入创建时间" maxlength="30" disabled/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="更新人" prop="updateBy">
                            <el-input v-model="form.updateBy" placeholder="请输入更新人" maxlength="30" disabled/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="更新时间" prop="updateTime">
                            <el-input v-model="form.updateTime" placeholder="请输入更新时间" maxlength="30" disabled/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>

</template>

<script setup name="Asset">
import { listAsset, addAsset, getAsset, updateAsset, uploadImg, typeTreeSelect } from '@/api/asset/manage' // 导入api接口
import { onMounted, nextTick, onUnmounted } from 'vue'
import QRCode from 'qrcodejs2-fix';
import { ref } from 'vue';
const { proxy } = getCurrentInstance()
const tempImageUrl = ref('') // 临时图片预览URL
const tempImageFile = ref(null) // 临时存储的文件对象
const tempImageName = ref('img') // 临时存储的文件名
const loading = ref(true) // 加载状态
const total = ref(0) // 数据总量
const assetList = ref([]) // 资产列表
const showSearch = ref(true) // 显示搜索条件
const dateRange = ref([]) // 日期范围
const openAdd = ref(false) // 新增弹窗
const openDetails = ref(false) // 详情弹窗
const title = ref("") // 弹窗标题
const remainingYears = ref("")
const { asset_normal_status, sys_normal_disable } = proxy.useDict("sys_normal_disable", "sys_user_sex", "asset_normal_status") // 字典
const materialTypeOptions = ref(undefined) // 资产类型下拉框选项
const enabledMaterialTypeOptions = ref(undefined)
const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCode: undefined,
        nickName: undefined,
        assetName: undefined,

    },
    rules: {
        //     dictName: [{ required: true, message: "字典名称不能为空", trigger: "blur" }],
        //     dictType: [{ required: true, message: "字典类型不能为空", trigger: "blur" }]
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询资产列表 */
function getList() {
    loading.value = true
    listAsset(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
        loading.value = false
        assetList.value = response.rows
        total.value = response.total
    })
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        assetName: undefined,
        // materialCode: undefined,
        specs: undefined,
        unit: undefined,
        purchasePrice: undefined,
        netAssetValue: undefined,
        nickName: undefined,
        usefulLifeYears: undefined,
        residualLifeYears: undefined,
        loaction: undefined,
        status: "0",
        remark: undefined,
        imgUrl: undefined
    }
    // 清理临时图片
    tempImageUrl.value = ''
    tempImageFile.value = null
    tempImageName.value = 'img'
    proxy.resetForm("addRef")
    proxy.resetForm("detailRef")
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = []
    proxy.resetForm("queryRef")
    queryParams.pageNum = 1
    handleQuery()
}

/** 查询按钮操作 */
function handleQuery() {
    getList()
}

/** 新增按钮操作 */
function handleAdd() {
    reset()
    //   getUser().then(response => {
    // postOptions.value = response.posts
    // roleOptions.value = response.roles
    openAdd.value = true
    title.value = "新增资产"
    //   })
}

/** 取消按钮 */
function cancel() {
    openAdd.value = false
    openDetails.value = false
    // 清理临时图片URL，防止内存泄漏
    if (tempImageUrl.value) {
        URL.revokeObjectURL(tempImageUrl.value)
    }
    reset()
}

/** 提交按钮 */
function submitForm() {
    const refName = form.value.id ? "detailRef" : "addRef"
    if (!proxy.$refs[refName]) {
        proxy.$modal.msgError("表单校验未通过，请重新检查提交内容")
        return
    }
    proxy.$refs[refName].validate(valid => {
        if (valid) {
            if (form.value.id != undefined) {
                updateAsset(form.value).then(response => {
                    console.log(form.value.materialCode);
                    proxy.$modal.msgSuccess("修改成功")
                    if (tempImageFile.value) {
                        let formData = new FormData()
                        formData.append("imgFile", tempImageFile.value, tempImageName.value)
                        formData.append("id", form.value.id)
                        uploadImg(formData).then(response => {
                            proxy.$modal.msgSuccess("上传成功")
                        })
                    }
                    openDetails.value = false
                    getList()
                })
            } else {
                addAsset(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功")
                    openAdd.value = false
                    getList()
                })
            }
        }
    })
}

/** 详情按钮 */
function handleDetails(row) {
    reset()
    getAsset(row.id).then(response => {
        form.value = response.data[0]
        openDetails.value = true
        title.value = "资产详情"
        // 计算剩余年限
        remainingYears.value = form.value.residualLifeYears - form.value.usefulLifeYears
        // 生成二维码
        nextTick(() => {
            createQrCode()
        })
        console.log('当前资产类别值:', form.value.materialCode)
        console.log('资产类型选项:', materialTypeOptions.value)
        
    })
}

/** 图片上传相关操作 */
function handleChange(uploadFile) {
    const file = uploadFile.raw
    const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isJPG) {
        proxy.$modal.msgError('上传头像图片只能是 JPG 或 PNG 格式!')
        console.log("上传头像图片只能是 JPG 或 PNG 格式!");
        return false
    }
    if (!isLt5M) {
        proxy.$modal.msgError('上传头像图片大小不能超过 5MB!')
        console.log("上传头像图片大小不能超过 5MB!");
        return false
    }
    // 清理旧的临时URL，防止内存泄漏
    if (tempImageUrl.value) {
        URL.revokeObjectURL(tempImageUrl.value)
    }
    // 清理当前的图片数据
    form.value.imgUrl = undefined
    // 创建临时预览URL
    tempImageUrl.value = URL.createObjectURL(file)
    tempImageFile.value = file
    tempImageName.value = file.name
    console.log(tempImageUrl.value);


    // 返回false阻止自动上传，但允许预览
    return true
}

/** 创建二维码 */
function createQrCode() {
    const qrcodeElement = proxy.$refs.qrcode;
    if (qrcodeElement) {
        // 清除之前的二维码
        qrcodeElement.innerHTML = '';
        let text = form.value;
        new QRCode(qrcodeElement, {
            text: text, // 二维码内容字符串
            width: 178, // 图像宽度
            height: 178, // 图像高度
            colorDark: '#000000', // 二维码前景色
            colorLight: '#ffffff', // 二维码背景色
            correctLevel: QRCode.CorrectLevel.H, // 容错级别
        });
    }
}

/** 查询资产类型下拉树结构 */
function getMaterialTypeTree() {
    typeTreeSelect().then(response => {
        materialTypeOptions.value = response.data
        console.log(materialTypeOptions.value);
        
    })
}

onMounted(() => {
    getList()
    getMaterialTypeTree()
})

// 组件卸载时清理临时图片URL
onUnmounted(() => {
    if (tempImageUrl.value) {
        URL.revokeObjectURL(tempImageUrl.value)
    }
})

</script>


<style>
.img {
    width: 178px;
    height: 178px;
    display: block;
}

.img-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.img-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.img-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
}
</style>
