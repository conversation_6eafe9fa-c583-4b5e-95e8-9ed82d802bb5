declare module 'qrcodejs2-fix' {
  interface QRCodeOptions {
    text?: string;
    width?: number;
    height?: number;
    colorDark?: string;
    colorLight?: string;
    correctLevel?: number;
  }

  class QRCode {
    static CorrectLevel: {
      L: number;
      M: number;
      Q: number;
      H: number;
    };

    constructor(element: HTMLElement | string, options?: QRCodeOptions);
    clear(): void;
    makeCode(text: string): void;
  }

  export = QRCode;
}