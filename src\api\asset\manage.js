import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

//查询资产列表
export function listAsset(query) {
    return request({
        url: '/assets/manage/list',
        method: 'get',
        params: query
    })
}

//新增资产
export function addAsset(data) {
    return request({
        url: '/assets/manage/add',
        method: 'post',
        data: data
    })
}

//查询资产详情
export function getAsset(assetId) {
    return request({
        url: '/assets/manage/detail/' + parseStrEmpty(assetId),
        method: 'get'
    })
}

//修改资产
export function updateAsset(data) {
    return request({
        url: '/assets/manage/update',
        method: 'put',
        data: data
    })
}

//上传图片
export function uploadImg(data) {
    return request({
        url: '/assets/manage/img/',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: data
    })
}

//查询资产类型下拉树结构
export function typeTreeSelect() {
    return request({
        url: '/assets/materialType/typeTree',
        method: 'get'
    })
}
